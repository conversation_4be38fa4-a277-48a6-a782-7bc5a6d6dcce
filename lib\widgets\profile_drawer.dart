import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';

class ProfileDrawer extends StatelessWidget {
  const ProfileDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final userProvider = Get.find<UserProvider>();
    final theme = Theme.of(context);

    return Drawer(
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: theme.colorScheme.primary.withValues(
                      alpha: 0.2,
                    ),
                    child: Icon(
                      Icons.account_circle,
                      size: 60,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    userProvider.currentUser?.nombre ?? 'Usuario',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.brightness_6),
            title: const Text('Modo Oscuro'),
            trailing: Switch(
              value: Get.isDarkMode,
              onChanged: (value) {
                Navigator.pop(context);
                Get.changeThemeMode(value ? ThemeMode.dark : ThemeMode.light);
              },
            ),
            onTap: () {
              Navigator.pop(context);
              Get.changeThemeMode(
                Get.isDarkMode ? ThemeMode.light : ThemeMode.dark,
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.favorite_outline),
            title: const Text('Favoritos'),
            onTap: () {
              Navigator.pop(context);
              Get.toNamed('/favorites');
            },
          ),
          ListTile(
            leading: const Icon(Icons.schedule),
            title: const Text('Programados'),
            onTap: () {
              Navigator.pop(context);
              // Navegar a programados
            },
          ),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text(
                'Cerrar Sesión',
                style: TextStyle(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                _logout(userProvider);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _logout(UserProvider userProvider) {
    // Limpiar datos de sesión
    userProvider.logout();

    // Navegar de vuelta a la pantalla de login
    Get.offAllNamed('/login');
  }
}
