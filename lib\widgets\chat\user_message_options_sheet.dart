import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/history_model.dart';
import '../../controllers/chat_controller.dart';
import '../../services/share_service.dart';

class UserMessageOptionsSheet extends StatelessWidget {
  final ChatHistory message;
  final String botId;

  const UserMessageOptionsSheet({
    super.key,
    required this.message,
    required this.botId,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChatController>(tag: botId);

    return Obx(() {
      final bool isFavorite = controller.isFavorite(message.rid);

      return SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.send),
              title: const Text('Reenviar'),
              onTap: () {
                Navigator.pop(context);
                _handleResendMessage(controller, context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Editar'),
              onTap: () {
                Navigator.pop(context);
                controller.editMessage(message.mensaje);
                FocusScope.of(context).requestFocus(FocusNode());
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copiar'),
              onTap: () {
                Navigator.pop(context);
                ShareService.copyToClipboard(message.mensaje, context);
              },
            ),
            ListTile(
              leading: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? Colors.red : null,
              ),
              title: Text(
                isFavorite ? 'Quitar de favoritos' : 'Agregar a favoritos',
              ),
              onTap: () {
                Navigator.pop(context);
                controller.toggleFavorite(message, context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('Programar'),
              onTap: () {
                Navigator.pop(context);
                // Implementar lógica para programar
              },
            ),
          ],
        ),
      );
    });
  }

  Future<void> _handleResendMessage(
    ChatController controller,
    BuildContext context,
  ) async {
    try {
      await controller.sendMessage(message: message.mensaje);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al reenviar el mensaje: $e')),
        );
      }
    }
  }
}
